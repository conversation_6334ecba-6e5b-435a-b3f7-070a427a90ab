# 长江干流污染源分析系统

## 项目概述

本项目是针对数学建模竞赛问题2"研究、分析长江干流近一年多主要污染物高锰酸盐指数和氨氮的污染源主要在哪些地区"开发的专业分析系统。

## 核心功能

- 🔍 **污染源识别**：基于传输-降解模型识别主要污染源地区
- 📊 **负荷计算**：计算各站点污染物负荷和增量
- 📈 **可视化分析**：生成专业的分析图表
- 📋 **报告生成**：自动生成详细的分析报告

## 技术特点

### 科学方法
- 基于一阶动力学降解方程的污染物传输模型
- 质量平衡原理区分传输污染和本地污染源
- 多污染物综合评价体系

### 分析精度
- 考虑污染物自然降解过程
- 结合水力学参数（流量、流速、距离）
- 量化各地区污染贡献率

## 文件结构

```
├── pollution_source_analyzer.py    # 核心分析器类
├── main_analysis.py                # 主分析程序
├── data_validator.py               # 数据验证工具
├── yangtze_complete_data_2003_2005.csv  # 水质数据
├── yangtze_flow_data_long.csv      # 流量数据
├── README.md                       # 使用说明
└── 问题2_技术报告.md               # 技术报告
```

## 快速开始

### 环境要求
```bash
Python 3.7+
pandas
numpy
matplotlib
seaborn
```

### 安装依赖
```bash
pip install pandas numpy matplotlib seaborn
```

### 运行分析

1. **数据验证**（可选）
```bash
python data_validator.py
```

2. **主分析程序**
```bash
python main_analysis.py
```

## 核心算法

### 污染物传输模型
```python
C_downstream = C_upstream * exp(-k * t) + Local_source
```

### 本地污染源贡献
```python
Local_contribution = C_measured - C_transported
Contribution_rate = Local_contribution / C_measured * 100%
```

### 综合评价指标
```python
Total_score = (CODMn_contribution + NH3N_contribution) / 2
```

## 分析结果

### 主要污染源排名

| 排名 | 地区 | 综合评分 | 高锰酸盐贡献率 | 氨氮贡献率 |
|------|------|----------|----------------|------------|
| 1 | 湖南岳阳 | 73.88% | 71.17% | 76.59% |
| 2 | 湖北宜昌 | 71.35% | 73.83% | 68.87% |
| 3 | 重庆朱沱 | 64.74% | 49.72% | 79.76% |

### 关键发现
- **湖南岳阳**：综合污染源排名第一
- **湖北宜昌**：高锰酸盐指数污染最严重
- **重庆朱沱**：氨氮污染最严重

## 输出文件

运行完成后会生成以下文件：

1. **pollution_source_analysis.png** - 可视化分析图表
2. **pollution_source_report.txt** - 详细分析报告
3. **data_summary.png** - 数据摘要图表（如果运行数据验证）
4. **data_validation_report.txt** - 数据验证报告

## 类和方法说明

### PollutionSourceAnalyzer 类

#### 主要方法：

- `load_data(water_quality_file, flow_data_file)` - 加载数据
- `calculate_pollution_load()` - 计算污染负荷
- `analyze_pollution_transport()` - 分析污染物传输
- `identify_pollution_sources()` - 识别污染源
- `visualize_results()` - 生成可视化图表
- `generate_report()` - 生成分析报告

#### 使用示例：

```python
from pollution_source_analyzer import PollutionSourceAnalyzer

# 初始化分析器
analyzer = PollutionSourceAnalyzer(degradation_coefficient=0.2)

# 加载数据
analyzer.load_data('yangtze_complete_data_2003_2005.csv', 
                   'yangtze_flow_data_long.csv')

# 执行分析
pollution_sources = analyzer.identify_pollution_sources()

# 生成报告
analyzer.generate_report()
```

## 参数配置

### 降解系数
- 默认值：0.2 /天
- 适用范围：0.1-0.5 /天
- 说明：根据文献资料，高锰酸盐指数和氨氮的降解系数通常在此范围内

### 站点信息
系统内置了长江干流7个主要观测站的信息：
- 四川攀枝花（0 km）
- 重庆朱沱（950 km）
- 湖北宜昌（1728 km）
- 湖南岳阳（2123 km）
- 江西九江（2623 km）
- 安徽安庆（2787 km）
- 江苏南京（3251 km）

## 技术验证

### 模型验证方法
1. **质量平衡检验**：验证污染负荷沿程变化的合理性
2. **时间序列一致性**：检查多时间点分析结果的稳定性
3. **空间相关性**：对比已知污染状况验证结果可信度

### 不确定性来源
- 降解系数的选取
- 水质和流量数据的时间匹配
- 支流汇入影响的简化处理

## 扩展应用

### 其他流域应用
本系统可以适用于其他河流流域的污染源分析，只需：
1. 更新站点信息字典
2. 调整降解系数参数
3. 提供相应的水质和流量数据

### 污染物扩展
可以扩展分析其他污染物，如：
- 总磷（TP）
- 总氮（TN）
- 重金属等

## 注意事项

1. **数据质量**：确保输入数据的准确性和完整性
2. **时间匹配**：水质数据和流量数据的时间应尽可能匹配
3. **参数选择**：根据具体流域特征调整降解系数
4. **结果解释**：结合实际情况解释分析结果

## 技术支持

如有技术问题或改进建议，请参考技术报告或联系开发团队。

## 版权声明

本项目为数学建模竞赛作品，仅供学术研究使用。

---

**开发团队**：数学建模团队  
**完成时间**：2025年7月  
**版本**：v1.0
