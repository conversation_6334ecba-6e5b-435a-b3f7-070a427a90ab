"""
长江干流污染源分析主程序
问题2：研究、分析长江干流近一年多主要污染物高锰酸盐指数和氨氮的污染源主要在哪些地区

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pollution_source_analyzer import PollutionSourceAnalyzer
import warnings
warnings.filterwarnings('ignore')

def main():
    """主分析函数"""
    print("=" * 60)
    print("长江干流污染源分析系统")
    print("问题2：主要污染物污染源地区识别")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = PollutionSourceAnalyzer(degradation_coefficient=0.2)
    
    try:
        # 1. 加载数据
        print("\n步骤1：数据加载")
        analyzer.load_data(
            water_quality_file='yangtze_complete_data_2003_2005.csv',
            flow_data_file='yangtze_flow_data_long.csv'
        )
        
        # 2. 计算污染负荷
        print("\n步骤2：污染负荷计算")
        pollution_load_data = analyzer.calculate_pollution_load()
        print("污染负荷数据预览：")
        print(pollution_load_data.head())
        
        # 3. 分析污染物传输
        print("\n步骤3：污染物传输分析")
        transport_analysis = analyzer.analyze_pollution_transport()
        print("传输分析数据预览：")
        print(transport_analysis[['downstream_station', 'codmn_local_contribution', 'nh3n_local_contribution']].head())
        
        # 4. 识别污染源
        print("\n步骤4：污染源识别")
        pollution_sources = analyzer.identify_pollution_sources()
        print("污染源分析结果：")
        print(pollution_sources[['station', 'province', 'codmn_contribution_rate', 'nh3n_contribution_rate', 'total_contribution_score']])
        
        # 5. 生成可视化图表
        print("\n步骤5：结果可视化")
        analyzer.visualize_results()
        
        # 6. 生成分析报告
        print("\n步骤6：生成分析报告")
        analyzer.generate_report()
        
        # 7. 详细分析结果输出
        print("\n" + "=" * 60)
        print("详细分析结果")
        print("=" * 60)
        
        # 按污染物分别分析
        print("\n【高锰酸盐指数污染源分析】")
        codmn_ranking = pollution_sources.sort_values('codmn_contribution_rate', ascending=False)
        print(f"{'排名':<4} {'地区':<15} {'本地贡献率':<10} {'负荷增量(kg/s)':<12}")
        print("-" * 45)
        for i, (_, row) in enumerate(codmn_ranking.iterrows(), 1):
            print(f"{i:<4} {row['station']:<15} {row['codmn_contribution_rate']:<10.2f}% {row['codmn_load_increase']:<12.4f}")
        
        print("\n【氨氮污染源分析】")
        nh3n_ranking = pollution_sources.sort_values('nh3n_contribution_rate', ascending=False)
        print(f"{'排名':<4} {'地区':<15} {'本地贡献率':<10} {'负荷增量(kg/s)':<12}")
        print("-" * 45)
        for i, (_, row) in enumerate(nh3n_ranking.iterrows(), 1):
            print(f"{i:<4} {row['station']:<15} {row['nh3n_contribution_rate']:<10.2f}% {row['nh3n_load_increase']:<12.4f}")
        
        # 8. 关键发现总结
        print("\n" + "=" * 60)
        print("关键发现总结")
        print("=" * 60)
        
        top_codmn = codmn_ranking.iloc[0]
        top_nh3n = nh3n_ranking.iloc[0]
        top_overall = pollution_sources.iloc[0]
        
        print(f"1. 高锰酸盐指数主要污染源：{top_codmn['station']} ({top_codmn['province']})")
        print(f"   - 本地贡献率：{top_codmn['codmn_contribution_rate']:.2f}%")
        print(f"   - 污染负荷增量：{top_codmn['codmn_load_increase']:.4f} kg/s")
        
        print(f"\n2. 氨氮主要污染源：{top_nh3n['station']} ({top_nh3n['province']})")
        print(f"   - 本地贡献率：{top_nh3n['nh3n_contribution_rate']:.2f}%")
        print(f"   - 污染负荷增量：{top_nh3n['nh3n_load_increase']:.4f} kg/s")
        
        print(f"\n3. 综合污染源排名第一：{top_overall['station']} ({top_overall['province']})")
        print(f"   - 综合评分：{top_overall['total_contribution_score']:.2f}%")
        print(f"   - 高锰酸盐贡献率：{top_overall['codmn_contribution_rate']:.2f}%")
        print(f"   - 氨氮贡献率：{top_overall['nh3n_contribution_rate']:.2f}%")
        
        # 9. 污染源空间分布特征
        print(f"\n4. 污染源空间分布特征：")
        high_pollution_areas = pollution_sources[pollution_sources['total_contribution_score'] > 20]
        if not high_pollution_areas.empty:
            print("   重点污染区域：")
            for _, row in high_pollution_areas.iterrows():
                print(f"   - {row['station']} ({row['province']})：{row['total_contribution_score']:.2f}%")
        
        # 10. 治理优先级建议
        print(f"\n5. 治理优先级建议：")
        print("   第一优先级：", end="")
        first_priority = pollution_sources[pollution_sources['total_contribution_score'] > 30]
        if not first_priority.empty:
            print(", ".join([f"{row['station']}" for _, row in first_priority.iterrows()]))
        else:
            print("无特别严重污染源")
        
        print("   第二优先级：", end="")
        second_priority = pollution_sources[
            (pollution_sources['total_contribution_score'] > 15) & 
            (pollution_sources['total_contribution_score'] <= 30)
        ]
        if not second_priority.empty:
            print(", ".join([f"{row['station']}" for _, row in second_priority.iterrows()]))
        else:
            print("无中等污染源")
        
        print("\n分析完成！结果已保存到文件中。")
        
    except Exception as e:
        print(f"分析过程中出现错误：{str(e)}")
        import traceback
        traceback.print_exc()

def analyze_temporal_patterns(analyzer):
    """分析时间变化模式"""
    if hasattr(analyzer, 'pollution_load_data'):
        print("\n时间变化模式分析：")
        
        # 按月份分析
        monthly_data = analyzer.pollution_load_data.copy()
        monthly_data['month'] = monthly_data['date'].dt.month
        monthly_avg = monthly_data.groupby(['station', 'month']).agg({
            'codmn': 'mean',
            'nh3n': 'mean'
        }).reset_index()
        
        print("各站点月度平均污染物浓度变化：")
        for station in analyzer.mainstream_station_info.keys():
            station_data = monthly_avg[monthly_avg['station'] == station]
            if not station_data.empty:
                codmn_range = station_data['codmn'].max() - station_data['codmn'].min()
                nh3n_range = station_data['nh3n'].max() - station_data['nh3n'].min()
                print(f"{station}: 高锰酸盐变化幅度 {codmn_range:.2f} mg/L, 氨氮变化幅度 {nh3n_range:.3f} mg/L")

if __name__ == "__main__":
    main()
