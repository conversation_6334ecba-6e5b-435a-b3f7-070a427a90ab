"""
长江干流污染源分析系统
专门用于识别和分析长江干流主要污染物（高锰酸盐指数和氨氮）的污染源分布

作者：数学建模团队
日期：2025年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class PollutionSourceAnalyzer:
    """污染源分析器"""
    
    def __init__(self, degradation_coefficient=0.2):
        """
        初始化污染源分析器
        
        Args:
            degradation_coefficient (float): 降解系数，单位：1/天，默认0.2
        """
        self.k = degradation_coefficient  # 降解系数
        self.water_quality_data = None
        self.flow_data = None
        self.mainstream_stations = None
        self.analysis_results = {}
        
        # 干流站点信息（按从上游到下游顺序）
        self.mainstream_station_info = {
            '四川攀枝花': {'distance': 0, 'province': '四川', 'order': 1},
            '重庆朱沱': {'distance': 950, 'province': '重庆', 'order': 2},
            '湖北宜昌': {'distance': 1728, 'province': '湖北', 'order': 3},
            '湖南岳阳': {'distance': 2123, 'province': '湖南', 'order': 4},
            '江西九江': {'distance': 2623, 'province': '江西', 'order': 5},
            '安徽安庆': {'distance': 2787, 'province': '安徽', 'order': 6},
            '江苏南京': {'distance': 3251, 'province': '江苏', 'order': 7}
        }
    
    def load_data(self, water_quality_file, flow_data_file):
        """
        加载水质和流量数据
        
        Args:
            water_quality_file (str): 水质数据文件路径
            flow_data_file (str): 流量数据文件路径
        """
        print("正在加载数据...")
        
        # 加载水质数据
        self.water_quality_data = pd.read_csv(water_quality_file)
        self.water_quality_data['date'] = pd.to_datetime(self.water_quality_data['date'])
        
        # 加载流量数据
        self.flow_data = pd.read_csv(flow_data_file)
        self.flow_data['date'] = pd.to_datetime(self.flow_data['date'])
        
        # 筛选干流站点数据
        mainstream_stations = list(self.mainstream_station_info.keys())
        self.mainstream_stations = self.water_quality_data[
            self.water_quality_data['station'].str.contains('|'.join([
                '攀枝花', '朱沱', '宜昌', '岳阳', '九江', '安庆', '南京'
            ]))
        ].copy()
        
        # 标准化站点名称
        station_mapping = {
            '四川攀枝花龙洞': '四川攀枝花',
            '重庆朱沱': '重庆朱沱', 
            '湖北宜昌南津关': '湖北宜昌',
            '湖南岳阳城陵矶': '湖南岳阳',
            '江西九江河西水厂': '江西九江',
            '安徽安庆皖河口': '安徽安庆',
            '江苏南京林山': '江苏南京'
        }
        
        for old_name, new_name in station_mapping.items():
            self.mainstream_stations.loc[
                self.mainstream_stations['station'].str.contains(old_name.split('_')[-1]), 
                'station'
            ] = new_name
        
        print(f"成功加载数据：")
        print(f"- 水质数据：{len(self.water_quality_data)} 条记录")
        print(f"- 流量数据：{len(self.flow_data)} 条记录")
        print(f"- 干流站点数据：{len(self.mainstream_stations)} 条记录")
    
    def calculate_pollution_load(self):
        """计算污染负荷"""
        print("正在计算污染负荷...")
        
        # 合并水质和流量数据
        merged_data = []
        
        for _, row in self.mainstream_stations.iterrows():
            station = row['station']
            date = row['date']
            
            # 查找对应的流量数据
            flow_row = self.flow_data[
                (self.flow_data['date'].dt.year == date.year) &
                (self.flow_data['date'].dt.month == date.month) &
                (self.flow_data['station'].str.contains(station.split('_')[-1]))
            ]
            
            if not flow_row.empty:
                flow_rate = flow_row.iloc[0]['flow_rate_m3s']
                flow_velocity = flow_row.iloc[0]['flow_velocity_ms']
                
                # 计算污染负荷 (kg/s)
                codmn_load = row['codmn'] * flow_rate / 1000  # mg/L * m³/s / 1000 = kg/s
                nh3n_load = row['nh3n'] * flow_rate / 1000
                
                merged_data.append({
                    'date': date,
                    'station': station,
                    'codmn': row['codmn'],
                    'nh3n': row['nh3n'],
                    'flow_rate': flow_rate,
                    'flow_velocity': flow_velocity,
                    'codmn_load': codmn_load,
                    'nh3n_load': nh3n_load,
                    'distance': self.mainstream_station_info.get(station, {}).get('distance', 0),
                    'order': self.mainstream_station_info.get(station, {}).get('order', 0)
                })
        
        self.pollution_load_data = pd.DataFrame(merged_data)
        self.pollution_load_data = self.pollution_load_data.sort_values(['date', 'order'])
        
        print(f"成功计算 {len(self.pollution_load_data)} 条污染负荷数据")
        return self.pollution_load_data
    
    def analyze_pollution_transport(self):
        """分析污染物传输和本地污染源贡献"""
        print("正在分析污染物传输...")
        
        if self.pollution_load_data is None:
            self.calculate_pollution_load()
        
        transport_analysis = []
        
        # 按日期分组分析
        for date, group in self.pollution_load_data.groupby('date'):
            group = group.sort_values('order')
            
            for i in range(1, len(group)):
                upstream = group.iloc[i-1]
                downstream = group.iloc[i]
                
                # 计算传输时间（天）
                distance_km = downstream['distance'] - upstream['distance']
                avg_velocity = (upstream['flow_velocity'] + downstream['flow_velocity']) / 2
                transport_time = (distance_km * 1000) / (avg_velocity * 86400)  # 转换为天
                
                # 计算理论传输浓度（考虑降解）
                codmn_transported = upstream['codmn'] * np.exp(-self.k * transport_time)
                nh3n_transported = upstream['nh3n'] * np.exp(-self.k * transport_time)
                
                # 计算本地污染源贡献
                codmn_local = downstream['codmn'] - codmn_transported
                nh3n_local = downstream['nh3n'] - nh3n_transported
                
                # 计算本地污染负荷增量
                codmn_load_increase = downstream['codmn_load'] - upstream['codmn_load'] * np.exp(-self.k * transport_time)
                nh3n_load_increase = downstream['nh3n_load'] - upstream['nh3n_load'] * np.exp(-self.k * transport_time)
                
                transport_analysis.append({
                    'date': date,
                    'upstream_station': upstream['station'],
                    'downstream_station': downstream['station'],
                    'distance_km': distance_km,
                    'transport_time_days': transport_time,
                    'codmn_upstream': upstream['codmn'],
                    'codmn_downstream': downstream['codmn'],
                    'codmn_transported': codmn_transported,
                    'codmn_local_contribution': codmn_local,
                    'nh3n_upstream': upstream['nh3n'],
                    'nh3n_downstream': downstream['nh3n'],
                    'nh3n_transported': nh3n_transported,
                    'nh3n_local_contribution': nh3n_local,
                    'codmn_load_increase': codmn_load_increase,
                    'nh3n_load_increase': nh3n_load_increase
                })
        
        self.transport_analysis = pd.DataFrame(transport_analysis)
        print(f"完成 {len(self.transport_analysis)} 个河段的传输分析")
        return self.transport_analysis
    
    def identify_pollution_sources(self):
        """识别主要污染源地区"""
        print("正在识别污染源...")
        
        if self.transport_analysis is None:
            self.analyze_pollution_transport()
        
        # 按下游站点统计污染源贡献
        pollution_sources = []
        
        for station in self.mainstream_station_info.keys():
            if station == '四川攀枝花':  # 最上游站点，无上游输入
                continue
                
            station_data = self.transport_analysis[
                self.transport_analysis['downstream_station'] == station
            ]
            
            if not station_data.empty:
                # 计算平均本地污染贡献
                avg_codmn_contribution = station_data['codmn_local_contribution'].mean()
                avg_nh3n_contribution = station_data['nh3n_local_contribution'].mean()
                avg_codmn_load_increase = station_data['codmn_load_increase'].mean()
                avg_nh3n_load_increase = station_data['nh3n_load_increase'].mean()
                
                # 计算相对贡献率
                downstream_data = self.pollution_load_data[
                    self.pollution_load_data['station'] == station
                ]
                if not downstream_data.empty:
                    avg_codmn_concentration = downstream_data['codmn'].mean()
                    avg_nh3n_concentration = downstream_data['nh3n'].mean()
                    
                    codmn_contribution_rate = (avg_codmn_contribution / avg_codmn_concentration * 100 
                                             if avg_codmn_concentration > 0 else 0)
                    nh3n_contribution_rate = (avg_nh3n_contribution / avg_nh3n_concentration * 100 
                                            if avg_nh3n_concentration > 0 else 0)
                    
                    pollution_sources.append({
                        'station': station,
                        'province': self.mainstream_station_info[station]['province'],
                        'distance': self.mainstream_station_info[station]['distance'],
                        'codmn_local_contribution': avg_codmn_contribution,
                        'nh3n_local_contribution': avg_nh3n_contribution,
                        'codmn_load_increase': avg_codmn_load_increase,
                        'nh3n_load_increase': avg_nh3n_load_increase,
                        'codmn_contribution_rate': codmn_contribution_rate,
                        'nh3n_contribution_rate': nh3n_contribution_rate,
                        'total_contribution_score': (codmn_contribution_rate + nh3n_contribution_rate) / 2
                    })
        
        self.pollution_sources = pd.DataFrame(pollution_sources)
        self.pollution_sources = self.pollution_sources.sort_values('total_contribution_score', ascending=False)
        
        print("污染源识别完成")
        return self.pollution_sources

    def visualize_results(self):
        """可视化分析结果"""
        print("正在生成可视化图表...")

        if self.pollution_sources is None:
            self.identify_pollution_sources()

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('长江干流污染源分析结果', fontsize=16, fontweight='bold')

        # 1. 污染源贡献率对比
        ax1 = axes[0, 0]
        stations = self.pollution_sources['station']
        codmn_rates = self.pollution_sources['codmn_contribution_rate']
        nh3n_rates = self.pollution_sources['nh3n_contribution_rate']

        x = np.arange(len(stations))
        width = 0.35

        ax1.bar(x - width/2, codmn_rates, width, label='高锰酸盐指数', alpha=0.8, color='skyblue')
        ax1.bar(x + width/2, nh3n_rates, width, label='氨氮', alpha=0.8, color='lightcoral')

        ax1.set_xlabel('观测站点')
        ax1.set_ylabel('本地污染贡献率 (%)')
        ax1.set_title('各地区污染源贡献率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels([s.split('_')[-1] for s in stations], rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 沿程污染物浓度变化
        ax2 = axes[0, 1]
        if hasattr(self, 'pollution_load_data'):
            avg_data = self.pollution_load_data.groupby('station').agg({
                'codmn': 'mean',
                'nh3n': 'mean',
                'distance': 'first'
            }).reset_index()
            avg_data = avg_data.sort_values('distance')

            ax2.plot(avg_data['distance'], avg_data['codmn'], 'o-', label='高锰酸盐指数', linewidth=2, markersize=6)
            ax2.plot(avg_data['distance'], avg_data['nh3n'], 's-', label='氨氮', linewidth=2, markersize=6)

            ax2.set_xlabel('距离 (km)')
            ax2.set_ylabel('浓度 (mg/L)')
            ax2.set_title('沿程污染物浓度变化')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        # 3. 污染负荷增量分析
        ax3 = axes[1, 0]
        codmn_loads = self.pollution_sources['codmn_load_increase']
        nh3n_loads = self.pollution_sources['nh3n_load_increase']

        ax3.bar(x - width/2, codmn_loads, width, label='高锰酸盐指数', alpha=0.8, color='orange')
        ax3.bar(x + width/2, nh3n_loads, width, label='氨氮', alpha=0.8, color='green')

        ax3.set_xlabel('观测站点')
        ax3.set_ylabel('污染负荷增量 (kg/s)')
        ax3.set_title('各地区污染负荷增量')
        ax3.set_xticks(x)
        ax3.set_xticklabels([s.split('_')[-1] for s in stations], rotation=45)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 综合污染源排名
        ax4 = axes[1, 1]
        total_scores = self.pollution_sources['total_contribution_score']
        colors = plt.cm.Reds(np.linspace(0.3, 0.9, len(total_scores)))

        bars = ax4.barh(range(len(stations)), total_scores, color=colors)
        ax4.set_yticks(range(len(stations)))
        ax4.set_yticklabels([s.split('_')[-1] for s in stations])
        ax4.set_xlabel('综合污染贡献评分')
        ax4.set_title('污染源综合排名')
        ax4.grid(True, alpha=0.3)

        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax4.text(width + 0.5, bar.get_y() + bar.get_height()/2,
                    f'{width:.1f}%', ha='left', va='center')

        plt.tight_layout()
        plt.savefig('pollution_source_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("可视化图表已生成并保存为 'pollution_source_analysis.png'")

    def generate_report(self):
        """生成分析报告"""
        print("正在生成分析报告...")

        if self.pollution_sources is None:
            self.identify_pollution_sources()

        report = []
        report.append("=" * 60)
        report.append("长江干流污染源分析报告")
        report.append("=" * 60)
        report.append(f"分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"降解系数：{self.k} /天")
        report.append("")

        # 执行摘要
        report.append("一、执行摘要")
        report.append("-" * 30)
        top_polluter = self.pollution_sources.iloc[0]
        report.append(f"主要污染源地区：{top_polluter['station']} ({top_polluter['province']})")
        report.append(f"综合污染贡献评分：{top_polluter['total_contribution_score']:.2f}%")
        report.append(f"高锰酸盐指数本地贡献率：{top_polluter['codmn_contribution_rate']:.2f}%")
        report.append(f"氨氮本地贡献率：{top_polluter['nh3n_contribution_rate']:.2f}%")
        report.append("")

        # 详细分析结果
        report.append("二、污染源详细分析")
        report.append("-" * 30)
        report.append(f"{'排名':<4} {'地区':<12} {'省份':<6} {'高锰酸盐贡献率':<12} {'氨氮贡献率':<10} {'综合评分':<8}")
        report.append("-" * 60)

        for i, row in self.pollution_sources.iterrows():
            rank = i + 1
            station = row['station'].split('_')[-1]
            province = row['province']
            codmn_rate = row['codmn_contribution_rate']
            nh3n_rate = row['nh3n_contribution_rate']
            total_score = row['total_contribution_score']

            report.append(f"{rank:<4} {station:<12} {province:<6} {codmn_rate:<12.2f}% {nh3n_rate:<10.2f}% {total_score:<8.2f}%")

        report.append("")

        # 污染源分类
        report.append("三、污染源分类")
        report.append("-" * 30)

        high_polluters = self.pollution_sources[self.pollution_sources['total_contribution_score'] > 30]
        medium_polluters = self.pollution_sources[
            (self.pollution_sources['total_contribution_score'] > 10) &
            (self.pollution_sources['total_contribution_score'] <= 30)
        ]
        low_polluters = self.pollution_sources[self.pollution_sources['total_contribution_score'] <= 10]

        report.append(f"高污染源地区 (贡献率>30%)：{len(high_polluters)} 个")
        for _, row in high_polluters.iterrows():
            report.append(f"  - {row['station']} ({row['province']}): {row['total_contribution_score']:.2f}%")

        report.append(f"中等污染源地区 (10%<贡献率≤30%)：{len(medium_polluters)} 个")
        for _, row in medium_polluters.iterrows():
            report.append(f"  - {row['station']} ({row['province']}): {row['total_contribution_score']:.2f}%")

        report.append(f"低污染源地区 (贡献率≤10%)：{len(low_polluters)} 个")
        for _, row in low_polluters.iterrows():
            report.append(f"  - {row['station']} ({row['province']}): {row['total_contribution_score']:.2f}%")

        report.append("")

        # 建议措施
        report.append("四、治理建议")
        report.append("-" * 30)
        report.append("1. 优先治理高污染源地区，重点关注：")
        for _, row in high_polluters.iterrows():
            report.append(f"   - {row['station']}：加强工业废水和生活污水处理")

        report.append("2. 加强中等污染源地区的监管和治理")
        report.append("3. 建立污染源动态监测系统")
        report.append("4. 实施流域协同治理机制")
        report.append("")

        # 保存报告
        report_text = "\n".join(report)
        with open('pollution_source_report.txt', 'w', encoding='utf-8') as f:
            f.write(report_text)

        print("分析报告已生成并保存为 'pollution_source_report.txt'")
        print("\n" + report_text)

        return report_text
